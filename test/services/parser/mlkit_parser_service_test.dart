import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';
import '../../test_data/sample_transactions.dart';

void main() {
  // Initialize Flutter bindings for tests that need platform services
  TestWidgetsFlutterBinding.ensureInitialized();
  group('MlKitParserService Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      // Always reset singleton before each test
      MlKitParserService.resetInstance();
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      // Reset singleton instance for clean tests
      print('Resetting MlKitParserService singleton instance');
      MlKitParserService.resetInstance();
    });

    group('Service Initialization', () {
      test('should create singleton instance with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final instance1 = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        final instance2 = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        expect(identical(instance1, instance2), isTrue);
      });

      test('should initialize with mock entity extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        expect(service, isNotNull);
      });

      test('should handle entity extractor errors gracefully', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Mock initialization error');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
        expect(service, isNotNull);

        // Service should still work by falling back to regex parsing
        final result = await service.parseTransaction('Spent \$25.50 on coffee');
        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
      });
    });

    group('Transaction Parsing with Mock Entity Extractor', () {
      test('should parse simple expense transactions with mock money entities', () async {
        // Test with mock entity extractor that returns money entities
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$25.50 on coffee',
          entityText: '\$25.50',
          start: 6,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle empty entity results and fall back to regex', () async {
        // Test with mock that returns no entities
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50)); // Should be parsed by fallback regex
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should parse income transactions with mock money entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Received \$2000 salary',
          entityText: '\$2000',
          start: 9,
          end: 15,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Received \$2000 salary');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2000.0));
        expect(result.transaction.type, equals(TransactionType.income));
      });

      test('should parse complex transactions with tags and multiple entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMultipleEntities(
          moneyText: '€150.75',
          moneyStart: 10,
          moneyEnd: 17,
          dateTimeText: 'yesterday',
          dateTimeStart: 25,
          dateTimeEnd: 34,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent €150.75 on dinner yesterday #restaurant');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(150.75));
        expect(result.transaction.tags, contains('restaurant'));
      });

      test('should handle non-money entities and fall back to regex', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithNonMoneyEntities();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50)); // Should be parsed by fallback
        expect(result.transaction.type, equals(TransactionType.expense));
      });
    });

    group('Fallback Behavior', () {
      test('should fall back to regex parsing when mock entity extractor throws error', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Mock parsing error');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        // Should fall back to regex parsing and succeed
        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should fall back to regex when mock returns empty entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final testTexts = [
          'Spent \$25.50 on coffee',
          'Received €100 salary',
          'Lent £50 to friend',
        ];

        for (final text in testTexts) {
          final result = await service.parseTransaction(text);

          // Should get a result from fallback regex parsing
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThan(0.0));
        }
      });

      test('should handle malformed inputs gracefully', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final malformedTexts = [
          'random text without amounts',
          'abc def ghi',
          '',
        ];

        for (final text in malformedTexts) {
          final result = await service.parseTransaction(text);

          // Should not throw, but may return failed result
          expect(result, isNotNull);
        }
      });
    });

    group('Mock Entity Extractor Unit Tests', () {
      test('should parse USD amounts correctly with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$25.50', 6, 12);
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should parse EUR amounts correctly with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createEURScenario('€150.75', 5, 12);
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Paid €150.75 for dinner');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(150.75));
      });

      test('should handle edge cases with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createEdgeCaseScenario(
          entityText: '\$1,234.56',
          start: 10,
          end: 19,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Transaction with \$1,234.56');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(1234.56));
      });

      test('should handle datetime entities with mock', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithDateTimeEntity(
          text: 'Spent money yesterday',
          entityText: 'yesterday',
          start: 12,
          end: 21,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25 yesterday');

        expect(result, isNotNull);
        // Should fall back to regex for amount since no money entity in mock
        expect(result.transaction.amount, equals(25.0));
      });
    });

    group('Currency Handling', () {
      test('should detect USD currency from mock money entity', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$25.50 on coffee',
          entityText: '\$25.50',
          start: 6,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$25.50 on coffee');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should detect EUR currency from mock money entity', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Paid €150.75 for dinner',
          entityText: '€150.75',
          start: 5,
          end: 12,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Paid €150.75 for dinner');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('EUR'));
      });

      test('should use default currency when no currency detected', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent 25.50 on something',
          entityText: '25.50',
          start: 6,
          end: 11,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 25.50 on something');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('EUR'));
      });
    });

    group('Category Learning', () {
      test('should learn and apply category associations with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'Coffee \$5.50 at my local cafe';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again - should use learned association
        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should prioritize learned categories over automatic detection', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50';
        const learnedCategoryId = 'beverages';

        // Learn a specific category
        await service.learnCategory(text, learnedCategoryId);

        // Parse again - should use learned category
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(learnedCategoryId));
        }
      });
    });

    group('Edge Cases', () {
      setUp(() {
        // Ensure fresh singleton for edge case tests
        MlKitParserService.resetInstance();
      });

      test('should handle empty input', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('');
        expect(result.hasError, isTrue);
      });

      test('should handle whitespace-only input', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('   \n\t  ');
        expect(result.hasError, isTrue);
      });

      test('should handle very long text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final longText = 'This is a very long transaction description that contains a lot of text and details about the purchase including the amount of \$25.50 spent on coffee at a local cafe with friends on a sunny afternoon during the weekend when everyone was relaxing and enjoying their time together';
        
        final result = await service.parseTransaction(longText);
        
        // Should handle long text gracefully
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
        }
      });

      test('should handle special characters and unicode', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Café & Restaurant! €25.50 🍽️ #food';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });

      test('should handle multiple amounts in text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent \$10 on coffee and \$15 on lunch';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          // Should pick one of the amounts (typically the first)
          expect([10.0, 15.0], contains(result.transaction.amount));
        }
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle parsing errors gracefully', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test various problematic inputs
        final problematicInputs = [
          'invalid input with no clear structure',
          '###@@@%%% weird characters',
          'amount without numbers',
          'very confusing transaction description',
        ];

        for (final text in problematicInputs) {
          expect(() async {
            await service.parseTransaction(text);
          }, returnsNormally, reason: 'Should not throw for: $text');
        }
      });

      test('should provide meaningful error messages', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('no amount here');
        
        if (result.hasError) {
          expect(result.error, isNotNull);
          expect(result.error!.length, greaterThan(0));
        }
      });
    });

    group('Transaction ID and Metadata', () {
      test('should generate unique transaction IDs', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final result1 = await service.parseTransaction(text);
        final result2 = await service.parseTransaction(text);
        
        if (result1.isSuccess && result2.isSuccess) {
          expect(result1.transaction.id, isNot(equals(result2.transaction.id)));
        }
      });

      test('should set appropriate timestamps', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final beforeParse = DateTime.now();
        final result = await service.parseTransaction(text);
        final afterParse = DateTime.now();
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.date.isAfter(beforeParse.subtract(const Duration(seconds: 1))), isTrue);
          expect(result.transaction.date.isBefore(afterParse.add(const Duration(seconds: 1))), isTrue);
        }
      });

      test('should create meaningful descriptions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50 downtown location';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.description, isNotEmpty);
          expect(result.transaction.description.toLowerCase(), contains('coffee'));
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final testTexts = [
          'Coffee \$5.50',
          'Gas \$40.00',
          'Groceries \$85.30',
          'Dinner \$45.99',
          'Movie \$12.50',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 50; i++) {
          for (final text in testTexts) {
            await service.parseTransaction(text);
          }
        }

        stopwatch.stop();
        
        // Should complete in reasonable time (this is generous to account for MLKit overhead)
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });

      test('should handle singleton access efficiently', () async {
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          await MlKitParserService.getInstance(mockStorage);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Reported Issues - Enhanced Fallback Logic', () {
      test('should fallback when MLKit returns empty entities', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // These should trigger fallback due to MLKit limitations
        final testCases = [
          'Spent ¥2500 on dinner',
          'Cost ¥3500 for shopping',
          'Paid ¥10000 for rent',
          'Beijing restaurant ¥45.50',
          'Tokyo sushi ¥1200',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          // Should succeed either via MLKit or fallback
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should parse successfully: $text');
          expect(result.transaction.amount, greaterThan(0),
                 reason: 'Should extract valid amount: $text');
        }
      });

      test('should fallback when MLKit fails to extract complete information', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'grocery shopping ¥100',
          'food shopping ¥50',
          'clothes shopping ¥200',
          '-¥500 for toys',
          '- \$25.50 coffee',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle via fallback: $text');
        }
      });
    });

    group('Reported Issues - Negative Number Handling', () {
      test('should detect negative amounts as expenses', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.type, equals(entry.value),
                   reason: 'Negative amount expense detection failed for: ${entry.key}');
            expect(result.transaction.amount, greaterThan(0),
                   reason: 'Amount should be positive for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Context-Aware Currency Detection', () {
      test('should detect CNY for Chinese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Shanghai taxi ¥25': 'CNY',
          'Guangzhou shopping ¥100': 'CNY',
          'China trip ¥500': 'CNY',
          'Chinese restaurant ¥80': 'CNY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'CNY detection failed for: ${entry.key}');
          }
        }
      });

      test('should detect JPY for Japanese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Tokyo sushi ¥1200': 'JPY',
          'Kyoto temple ¥500': 'JPY',
          'Osaka shopping ¥800': 'JPY',
          'Japan travel ¥2000': 'JPY',
          'Japanese restaurant ¥900': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'JPY detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Category Keyword Conflicts', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'supermarket shopping ¥75': 'food',
          'restaurant shopping ¥25': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Category conflict resolution failed for: ${entry.key}');
          }
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'clothes shopping ¥100': 'shopping',
          'electronics shopping ¥500': 'shopping',
          'gadget shopping ¥200': 'shopping',
          'fashion shopping ¥150': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Shopping category detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Real-world Integration', () {
      test('should parse real-world transaction examples', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final realWorldCases = SampleTransactions.realWorldExamples;

        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);

          expect(result, isNotNull);

          // Should either succeed or require user input (not fail completely)
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle real-world case: $text');

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount),
                   reason: 'Amount mismatch for real-world case: $text');
          }
        }
      });
    });

    group('Learning Bypass Functionality', () {
      test('should bypass ML Kit parsing when learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'coffee at starbucks';
        const expectedType = TransactionType.expense;
        const expectedCategoryId = 'food';

        // First, learn an association
        await service.learnCategory(text, expectedCategoryId);

        // Parse the same text - should bypass ML Kit and use learned association
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.type, equals(expectedType));
        expect(result.transaction.categoryId, equals(expectedCategoryId));
        expect(result.transaction.description, equals(text));
      });

      test('should fall back to ML Kit when no learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'unknown transaction text';

        // Parse without any learned association
        final result = await service.parseTransaction(text);

        // Should proceed with normal ML Kit parsing
        expect(result, isNotNull);
        // Result can be success, soft fail, or hard fail depending on ML Kit capabilities
      });

      test('should handle learned associations with partial text matching', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const fullText = 'payment to uber technologies inc';
        const partialText = 'uber ride';
        const categoryId = 'transport';

        // Learn with full text
        await service.learnCategory(fullText, categoryId);

        // Should match with partial text
        final result = await service.parseTransaction(partialText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle vendor name extraction in learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const originalText = 'Payment to McDonald\'s Restaurant #1234';
        const testText = 'mcdonalds breakfast';
        const categoryId = 'food';

        // Learn with original text
        await service.learnCategory(originalText, categoryId);

        // Should match with vendor name
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle case insensitive learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const learnText = 'Starbucks Coffee';
        const testText = 'STARBUCKS COFFEE';
        const categoryId = 'food';

        // Learn with mixed case
        await service.learnCategory(learnText, categoryId);

        // Should match with different case
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle learning errors gracefully without breaking parsing', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'test transaction';

        // Corrupt the learned associations storage
        await mockStorage.setString('learned_associations', 'invalid json');

        // Parsing should still work even if learning lookup fails
        final result = await service.parseTransaction(text);
        expect(result, isNotNull);
        // Should fall back to normal ML Kit parsing
      });

      test('should prioritize learned associations over mock entity extractor results', () async {
        // Create a mock that would normally return money entities
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'salary payment \$2000',
          entityText: '\$2000',
          start: 15,
          end: 21,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'salary payment \$2000';
        const learnedCategoryId = 'test';

        // Learn an association (should take priority over entity extraction)
        await service.learnCategory(text, learnedCategoryId);

        // Parse - should use learned association, not entity extractor result
        final result = await service.parseTransaction(text);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(learnedCategoryId));
        // The learned association should take priority over entity extraction
      });

      test('should handle multiple learned associations correctly with mock extractor', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final testCases = [
          {'text': 'starbucks coffee \$5.50', 'category': 'food'},
          {'text': 'uber ride \$15.00', 'category': 'transport'},
          {'text': 'amazon purchase \$25.99', 'category': 'shopping'},
        ];

        // Learn multiple associations
        for (final testCase in testCases) {
          await service.learnCategory(
              testCase['text'] as String,
              testCase['category'] as String);
        }

        // Test each association
        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);
          expect(result.isSuccess, isTrue,
              reason: 'Should find learned association for: ${testCase['text']}');
          expect(result.transaction.categoryId, equals(testCase['category']),
              reason: 'Category mismatch for: ${testCase['text']}');
        }
      });
    });

    group('Soft-Fail Logic Validation', () {
      test('should return needsCategory when no category can be determined', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Use text that has amount and type indicators but no recognizable category
        const unknownText = 'spent 25.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should either need category selection or succeed with 'unknown' placeholder
        if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.isSuccess) {
          // If it succeeds, it should use 'unknown' placeholder, not 'other'
          expect(result.transaction.categoryId, equals('unknown'));
        } else {
          fail('Unexpected parse result: ${result.status}');
        }
      });

      test('should not default to other category for unknown transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const unknownText = 'spent 42.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should never return 'other' as category - this was the bug we fixed
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should either be 'unknown' (placeholder) or require user input
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should still succeed for learned associations even with soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const text = 'spent 30.00 at learned vendor';
        const expectedCategory = 'shopping';

        // Learn an association first
        await service.learnCategory(text, expectedCategory);

        // Parse the same text - should succeed with learned category
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(expectedCategory));
        expect(result.status, equals(ParseStatus.success));
      });

      test('should handle edge cases in soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'spent 15.00 at qwerty123',
          'paid 25.50 for zxcvbn',
          'bought asdfgh for 10.00',
        ];

        for (final testText in testCases) {
          final result = await service.parseTransaction(testText);

          // Should never return 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: $testText');

          // Should either be unknown placeholder or need category selection
          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals('unknown'),
                reason: 'Should use unknown placeholder for: $testText');
          } else {
            expect(result.needsCategorySelection, isTrue,
                reason: 'Should need category selection for: $testText');
          }
        }
      });

      test('should maintain consistency between MlKit and Fallback parser soft-fail behavior', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Test text that will likely fall back to regex parser
        const textWithoutMLKitEntities = 'qwerty transaction 50.00';

        final result = await service.parseTransaction(textWithoutMLKitEntities);

        // Regardless of which parser handles it, should not return 'other'
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should follow same soft-fail logic
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should validate soft-fail logic with various transaction types', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': 'spent 20.00 at qwerty place', 'type': TransactionType.expense},
          {'text': 'received 100.00 from zxcvbn source', 'type': TransactionType.income},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          // Should detect correct transaction type
          expect(result.transaction.type, equals(testCase['type']),
              reason: 'Type detection failed for: ${testCase['text']}');

          // Should not default to 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: ${testCase['text']}');
        }
      });
    });

    group('Currency Bug Regression Tests', () {
      test('should use default currency for learned associations when no explicit currency', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // First, parse a transaction to trigger learning (this would normally require category confirmation)
        // For this test, we'll simulate the learned association scenario directly
        final result1 = await service.parseTransaction('1000 clothes');

        // The transaction should use VND currency, not USD
        expect(result1.transaction.currencyCode, equals('VND'),
            reason: 'Should use default currency VND, not hardcoded USD');
      });

      test('should use explicit currency symbols even with default currency set', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Parse transaction with explicit USD symbol
        final result = await service.parseTransaction('\$1000 clothes');

        // Should use USD from symbol, not default VND
        expect(result.transaction.currencyCode, equals('USD'),
            reason: 'Explicit currency symbol should override default currency');
      });

      test('should use explicit currency codes even with default currency set', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Parse transaction with explicit EUR code
        final result = await service.parseTransaction('1000 EUR clothes');

        // Should use EUR from code, not default VND
        expect(result.transaction.currencyCode, equals('EUR'),
            reason: 'Explicit currency code should override default currency');
      });
    });

    group('Number Abbreviation Support Tests', () {
      test('should parse basic abbreviations correctly', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100k food', 'expectedAmount': 100000.0},
          {'text': '2.5M salary', 'expectedAmount': 2500000.0},
          {'text': '1.2B investment', 'expectedAmount': 1200000000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle case variations in abbreviations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100K food', 'expectedAmount': 100000.0},
          {'text': '2m salary', 'expectedAmount': 2000000.0},
          {'text': '1.5b investment', 'expectedAmount': 1500000000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Case-insensitive parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency symbols', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '\$100k shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '€2.5M bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '₫1.5k transport', 'expectedAmount': 1500.0, 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency codes', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100k USD shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '2.5M EUR bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '1.2B VND investment', 'expectedAmount': 1200000000.0, 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency code failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency code detection failed for: ${testCase['text']}');
        }
      });

      test('should handle thousands separators with abbreviations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '1,500k shopping', 'expectedAmount': 1500000.0},
          {'text': '2,500.50M investment', 'expectedAmount': 2500500000.0},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Thousands separator parsing failed for: ${testCase['text']}');
        }
      });

      test('should maintain backward compatibility with non-abbreviated amounts', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '\$100.50 food', 'expectedAmount': 100.50},
          {'text': '1,500 euros shopping', 'expectedAmount': 1500.0},
          {'text': '250.75 transport', 'expectedAmount': 250.75},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Backward compatibility failed for: ${testCase['text']}');
        }
      });
    });

    group('Embedded Numbers vs Abbreviations', () {
      test('should parse "com trua tai Lux68 2m" as 2,000,000 not 68', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateLux68Scenario();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai Lux68 2m');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2000000.0),
            reason: 'Should parse "2m" as 2 million, not "68" from Lux68');
      });

      test('should parse "dinner at Cafe123 500k" as 500,000 not 123', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'dinner at Cafe123 500k',
          embeddedNumber: '123',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('dinner at Cafe123 500k');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(500000.0),
            reason: 'Should parse "500k" as 500,000, not "123" from Cafe123');
      });

      test('should parse "shopping Mall456 1.5M" as 1,500,000 not 456', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'shopping Mall456 1.5M',
          embeddedNumber: '456',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('shopping Mall456 1.5M');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(1500000.0),
            reason: 'Should parse "1.5M" as 1.5 million, not "456" from Mall456');
      });
    });

    group('Vendor Name Detection', () {
      test('should ignore Hotel789 embedded numbers when larger amounts exist', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'Hotel789 1000k stay',
          embeddedNumber: '789',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Hotel789 1000k stay');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(1000000.0),
            reason: 'Should parse "1000k" as 1 million, ignoring embedded "789"');
      });

      test('should accept \$789 even if embedded when has currency context', () async {
        final mockExtractor = MockEntityExtractorFactory.createVendorNameScenario(
          fullText: 'Hotel789 \$789 fee',
          embeddedNumber: '\$789',
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Hotel789 \$789 fee');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(789.0),
            reason: 'Should accept \$789 as valid amount due to currency context');
      });

      test('should choose largest reasonable amount with multiple embedded numbers', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '123', 'start': 5, 'end': 8},
            {'text': '456', 'start': 13, 'end': 16},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Shop123 Mall456 2.5M purchase');

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(2500000.0),
            reason: 'Should choose "2.5M" over embedded numbers 123 and 456');
      });
    });

    group('Mock Entity Extractor Tests', () {
      test('should handle Lux68 scenario with mock returning specific ML Kit results', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateLux68Scenario();
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('com trua tai Lux68 2m');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(2000000.0),
            reason: 'Mock should simulate ML Kit finding "68" but parser should choose "2m"');
      });

      test('should verify selection algorithm with controlled inputs', () async {
        final mockExtractor = MockEntityExtractorFactory.simulateMultipleMoneyEntities(
          entities: [
            {'text': '50', 'start': 10, 'end': 12},
            {'text': '100', 'start': 20, 'end': 23},
          ],
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Restaurant50 Hotel100 5M total');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(5000000.0),
            reason: 'Should select "5M" over smaller embedded amounts');
      });

      test('should verify fallback behavior when ML Kit fails', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithError('Simulated ML Kit failure');
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent 2.5M on equipment');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(2500000.0),
            reason: 'Should fall back to regex parsing when ML Kit fails');
      });
    });

    group('Regression Tests', () {
      test('should maintain simple amounts like \$100 food functionality', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$100 on food',
          entityText: '\$100',
          start: 6,
          end: 10,
        );
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        final result = await service.parseTransaction('Spent \$100 on food');

        expect(result, isNotNull);
        expect(result.transaction.amount, equals(100.0));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should maintain currency detection accuracy', () async {
        final testCases = [
          {'text': 'Spent €50 on dinner', 'expectedCurrency': 'EUR'},
          {'text': 'Paid £25 for taxi', 'expectedCurrency': 'GBP'},
          {'text': 'Cost ¥1000 for shopping', 'expectedCurrency': 'JPY'},
        ];

        for (final testCase in testCases) {
          final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
          final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

          final result = await service.parseTransaction(testCase['text'] as String);

          expect(result, isNotNull);
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });

      test('should not affect learned associations', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // Will fall back to regex
        final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

        const text = 'Starbucks coffee \$5.50';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again - should use learned association
        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });
    });
  });
}
