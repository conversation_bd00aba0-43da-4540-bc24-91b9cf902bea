import '../../lib/services/parser/entity_extractor_base.dart';

/// Mock implementation of EntityAnnotationBase for testing
class MockEntityAnnotation implements EntityAnnotationBase {
  @override
  final String text;
  
  @override
  final int start;
  
  @override
  final int end;
  
  @override
  final EntityType entityType;

  MockEntityAnnotation({
    required this.text,
    required this.start,
    required this.end,
    required this.entityType,
  });
}

/// Mock implementation of EntityExtractorBase for testing
/// This allows configuring return values and simulating various scenarios
class MockEntityExtractor implements EntityExtractorBase {
  List<EntityAnnotationBase> _mockResults = [];
  bool _shouldThrowError = false;
  bool _isInitialized = true;
  String? _errorMessage;

  /// Set the entities that this mock should return when annotateText is called
  void setMockResults(List<EntityAnnotationBase> results) {
    _mockResults = results;
  }

  /// Configure the mock to throw an error when annotateText is called
  void setShouldThrowError(bool shouldThrow, [String? errorMessage]) {
    _shouldThrowError = shouldThrow;
    _errorMessage = errorMessage;
  }

  /// Configure the initialization state
  void setInitialized(bool initialized) {
    _isInitialized = initialized;
  }

  /// Reset the mock to default state
  void reset() {
    _mockResults = [];
    _shouldThrowError = false;
    _isInitialized = true;
    _errorMessage = null;
  }

  @override
  Future<List<EntityAnnotationBase>> annotateText(String text) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock entity extraction error');
    }
    return _mockResults;
  }

  @override
  Future<void> close() async {
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized;
}

/// Factory class for creating common mock scenarios
class MockEntityExtractorFactory {
  
  /// Create a mock extractor that returns empty results (no entities found)
  static MockEntityExtractor createEmpty() {
    final mock = MockEntityExtractor();
    mock.setMockResults([]);
    return mock;
  }

  /// Create a mock extractor that throws an error
  static MockEntityExtractor createWithError([String? errorMessage]) {
    final mock = MockEntityExtractor();
    mock.setShouldThrowError(true, errorMessage);
    return mock;
  }

  /// Create a mock extractor with a simple money entity
  static MockEntityExtractor createWithMoneyEntity({
    required String text,
    required String entityText,
    required int start,
    required int end,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: start,
        end: end,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor with a datetime entity
  static MockEntityExtractor createWithDateTimeEntity({
    required String text,
    required String entityText,
    required int start,
    required int end,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: start,
        end: end,
        entityType: EntityType.dateTime,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor with both money and datetime entities
  static MockEntityExtractor createWithMultipleEntities({
    required String moneyText,
    required int moneyStart,
    required int moneyEnd,
    required String dateTimeText,
    required int dateTimeStart,
    required int dateTimeEnd,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: moneyText,
        start: moneyStart,
        end: moneyEnd,
        entityType: EntityType.money,
      ),
      MockEntityAnnotation(
        text: dateTimeText,
        start: dateTimeStart,
        end: dateTimeEnd,
        entityType: EntityType.dateTime,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor for testing USD currency scenarios
  static MockEntityExtractor createUSDScenario(String amountText, int start, int end) {
    return createWithMoneyEntity(
      text: 'Spent $amountText on lunch',
      entityText: amountText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor for testing EUR currency scenarios
  static MockEntityExtractor createEURScenario(String amountText, int start, int end) {
    return createWithMoneyEntity(
      text: 'Paid $amountText for dinner',
      entityText: amountText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor for testing edge cases (very large amounts, decimals, etc.)
  static MockEntityExtractor createEdgeCaseScenario({
    required String entityText,
    required int start,
    required int end,
  }) {
    return createWithMoneyEntity(
      text: 'Transaction with $entityText',
      entityText: entityText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor that simulates ML Kit finding entities but no money entity
  static MockEntityExtractor createWithNonMoneyEntities() {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: '<EMAIL>',
        start: 10,
        end: 25,
        entityType: EntityType.email,
      ),
      MockEntityAnnotation(
        text: '123 Main St',
        start: 30,
        end: 41,
        entityType: EntityType.address,
      ),
    ]);
    return mock;
  }

  /// Add support for creating specific ML Kit entity scenarios
  static void addMoneyEntityToMock(MockEntityExtractor mock, String text, int start, int end, String entityText) {
    final currentResults = List<EntityAnnotationBase>.from(mock._mockResults);
    currentResults.add(MockEntityAnnotation(
      text: entityText,
      start: start,
      end: end,
      entityType: EntityType.money,
    ));
    mock.setMockResults(currentResults);
  }

  /// Helper that sets up the exact bug scenario: returns "68" as a money entity from position of "Lux68"
  static MockEntityExtractor simulateLux68Scenario() {
    final mock = MockEntityExtractor();
    // In "com trua tai Lux68 2m", "Lux68" starts at position 12 and "68" is at positions 15-17
    mock.setMockResults([
      MockEntityAnnotation(
        text: '68',
        start: 15,
        end: 17,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Helper to test selection logic between multiple candidates
  static MockEntityExtractor simulateMultipleMoneyEntities({
    required List<Map<String, dynamic>> entities,
  }) {
    final mock = MockEntityExtractor();
    final annotations = entities.map((entity) => MockEntityAnnotation(
      text: entity['text'] as String,
      start: entity['start'] as int,
      end: entity['end'] as int,
      entityType: EntityType.money,
    )).toList();
    mock.setMockResults(annotations);
    return mock;
  }

  /// Helper to calculate start/end positions for entity text within full input
  static Map<String, int> calculateEntityPosition(String fullText, String entityText) {
    final index = fullText.indexOf(entityText);
    if (index == -1) {
      throw ArgumentError('Entity text "$entityText" not found in full text "$fullText"');
    }
    return {
      'start': index,
      'end': index + entityText.length,
    };
  }

  /// Create mock for vendor name embedded number scenarios
  static MockEntityExtractor createVendorNameScenario({
    required String fullText,
    required String embeddedNumber,
  }) {
    final position = calculateEntityPosition(fullText, embeddedNumber);
    return createWithMoneyEntity(
      text: fullText,
      entityText: embeddedNumber,
      start: position['start']!,
      end: position['end']!,
    );
  }
}
