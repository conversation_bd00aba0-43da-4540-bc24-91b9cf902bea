
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../utils/currency_utils.dart';
import '../../utils/amount_utils.dart';
import '../storage_service.dart';
import 'category_finder_service.dart';
import 'fallback_parser_service.dart';
import 'learned_association_service.dart';
import 'entity_extractor_base.dart';
import 'real_entity_extractor.dart';

/// Main orchestrator service that coordinates the entire parsing pipeline using ML Kit with fallback
class MlKitParserService {
  static MlKitParserService? _instance;

  EntityExtractorBase? _entityExtractor;
  late CategoryFinderService _categoryFinder;
  late FallbackParserService _fallbackParser;
  late StorageService _storageService;
  late LearnedAssociationService _learnedAssociationService;
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;
  bool _mlKitAvailable = false;

  MlKitParserService._();

  /// Factory constructor to get singleton instance
  /// [entityExtractor] - Optional entity extractor for dependency injection (mainly for testing)
  static Future<MlKitParserService> getInstance(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._storageService = storageService;
      _instance!._categoryFinder = CategoryFinderService(storageService);
      _instance!._fallbackParser = FallbackParserService(storageService);
      _instance!._learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
      await _instance!._initialize(entityExtractor);
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance?._entityExtractor?.close();
    _instance = null;
  }

  /// Initialize ML Kit models with fallback support
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initialize([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      print('Using injected entity extractor');
      _isInitialized = true;
      return;
    }

    try {
      print('Attempting to initialize ML Kit...');
      // Create and initialize the real entity extractor
      final realExtractor = RealEntityExtractor();
      await realExtractor.initialize();

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      print('ML Kit initialized successfully');

    } catch (e) {
      print('Error initializing ML Kit: $e');
      print('Falling back to regex-based parsing');
      _mlKitAvailable = false;
      // ML Kit will be null, will use fallback parser
    }

    _isInitialized = true;
  }

  /// Main entry point for parsing transactions
  Future<ParseResult> parseTransaction(String text) async {
    try {
      if (!_isInitialized) {
        await _initialize();
      }

      // NEW: Check learned associations first
      final learnedAssociation = await _learnedAssociationService.getAssociation(text);
      if (learnedAssociation != null) {
        final transaction = await _buildTransactionFromAssociation(text, learnedAssociation);
        return ParseResult.success(transaction);
      }

      // If ML Kit is available, try ML Kit first
      if (_mlKitAvailable && _entityExtractor != null) {
        try {
          return await _parseWithMLKit(text);
        } catch (e) {
          print('ML Kit parsing failed, falling back to regex: $e');
          // Fall through to regex fallback
        }
      }

      // Use fallback parser (regex-based)
      return await _fallbackParser.parseTransaction(text);

    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Parse using ML Kit
  Future<ParseResult> _parseWithMLKit(String text) async {
    print('Parsing transaction with ML Kit...');

    // Extract entities using ML Kit
    final entities = await _entityExtractor!.annotateText(text);

    // Enhanced fallback logic: check if MLKit returns incomplete results
    if (entities.isEmpty) {
      print('MLKit returned no entities, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // Collect all money entities from ML Kit into a list of candidates
    final List<Map<String, dynamic>> mlKitCandidates = [];
    DateTime? extractedDate;
    String remainingText = text;
    bool foundMoneyEntity = false;

    for (final entity in entities) {
      // Check entity type using the abstraction's entity type enumeration
      if (entity.entityType == EntityType.money) {
        foundMoneyEntity = true;
        final result = _parseMoneyEntity(entity, text);
        if (result != null) {
          mlKitCandidates.add({
            'amount': result['amount'],
            'currency': result['currency'],
            'entity': entity,
          });
        }
        remainingText = _removeEntityFromText(remainingText, entity);
      } else if (entity.entityType == EntityType.dateTime) {
        extractedDate = _parseDateTimeEntity(entity);
        remainingText = _removeEntityFromText(remainingText, entity);
      }
    }

    // Enhanced fallback logic: if no money entity found, use fallback parser
    if (!foundMoneyEntity) {
      print('MLKit found entities but no money entity, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // If all ML Kit candidates were filtered out (e.g., embedded in vendor names),
    // try to find non-vendor-embedded amounts using custom logic
    if (mlKitCandidates.isEmpty) {
      final customAmount = _extractNonVendorAmount(text);
      if (customAmount != null) {
        // Use the custom amount to build a transaction
        final amount = customAmount['amount'] as double;
        final currency = customAmount['currency'] as String?;

        // Determine transaction type
        final type = _detectTransactionType(text) ?? TransactionType.expense;

        // Find category
        final categoryId = await _categoryFinder.findCategory(text, type);

        // Create the transaction
        final transaction = Transaction(
          id: _uuid.v4(),
          amount: amount,
          type: type,
          categoryId: categoryId ?? 'unknown',
          date: DateTime.now(),
          description: text.trim(),
          tags: _extractTags(text),
          currencyCode: currency ?? 'USD',
        );

        // Return result indicating if category selection is needed
        if (categoryId == null) {
          return ParseResult.needsCategory(transaction);
        } else {
          return ParseResult.success(transaction);
        }
      }


      return await _fallbackParser.parseTransaction(text);
    }

    // Use custom amount extraction that prioritizes abbreviations over plain numbers
    final customAmountResult = _extractNonVendorAmount(text);
    double? finalAmount;
    String? finalCurrency;

    if (customAmountResult != null && customAmountResult['amount'] != null) {
      // Use smart amount selection to choose between candidates
      final selectedAmount = _selectBestAmount(
        mlKitCandidates,
        customAmountResult['amount'] as double,
        text
      );

      if (selectedAmount != null) {
        finalAmount = selectedAmount['amount'] as double?;
        finalCurrency = selectedAmount['currency'] as String?;

        // If we selected the custom amount result, use its currency
        if (selectedAmount['source'] == 'amountUtils') {
          finalCurrency = finalCurrency ?? customAmountResult['currency'] as String?;
        }
      }
    } else if (mlKitCandidates.isNotEmpty) {
      // No AmountUtils result, use first ML Kit candidate
      final firstCandidate = mlKitCandidates.first;
      finalAmount = firstCandidate['amount'] as double?;
      finalCurrency = firstCandidate['currency'] as String?;
    }

    // Enhanced fallback logic: if amount extraction still fails, use fallback parser
    if (finalAmount == null) {
      print('MLKit found money entity but amount extraction failed, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // If no currency found, get default currency
    if (finalCurrency == null) {
      finalCurrency = await _storageService.getDefaultCurrency();
    }

    // Detect transaction type with negative amount information
    final isNegativeAmount = text.trim().startsWith('-');
    final type = _detectTransactionType(text, isNegativeAmount: isNegativeAmount);

    // If transaction type is unclear, return needsType status
    if (type == null) {
      // Create partial transaction with default expense type for type disambiguation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: finalAmount,
        type: TransactionType.expense, // Default type, will be updated by user selection
        categoryId: 'other',
        date: extractedDate ?? DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: finalCurrency,
      );

      return ParseResult.needsType(partialTransaction);
    }

    // Find category using the category finder service
    final categoryId = await _categoryFinder.findCategory(remainingText, type);

    // Create the transaction - use 'unknown' placeholder when no category found
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: finalAmount,
      type: type,
      categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
      date: extractedDate ?? DateTime.now(),
      description: _createDescription(text),
      tags: _extractTags(text),
      currencyCode: finalCurrency,
    );

    // Return result indicating if category selection is needed
    if (categoryId == null) {
      return ParseResult.needsCategory(transaction);
    } else {
      return ParseResult.success(transaction);
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    await _learnedAssociationService.learn(text, categoryId: categoryId);
  }

  /// Parse money entity from ML Kit with enhanced validation
  Map<String, dynamic>? _parseMoneyEntity(EntityAnnotationBase entity, String fullText) {
    try {
      // Extract the numeric value and currency from the money entity text
      final entityText = entity.text;

      // Enhanced regex pattern to include abbreviations: k/K, m/M, b/B
      final numericRegex = RegExp(r'(\d+(?:,\d{3})*(?:\.\d+)?[kKmMbB]?|\d+\.\d+[kKmMbB]?)');
      final match = numericRegex.firstMatch(entityText);
      double? amount;
      String? currency;

      if (match != null) {
        final numericString = match.group(1)!.replaceAll(',', '');

        // Use AmountUtils.parseAbbreviatedNumber() when abbreviation suffixes are detected
        if (numericString.toLowerCase().contains(RegExp(r'[kmb]$'))) {
          amount = AmountUtils.parseAbbreviatedNumber(numericString);
        } else {
          amount = double.tryParse(numericString);
        }
      }

      // Add validation to skip amounts that appear to be embedded in vendor names
      if (amount != null && _isEmbeddedInVendorName(entityText, fullText)) {
        // Only accept embedded numbers if they have clear currency context
        if (!_hasCurrencyContext(entityText)) {
          return null; // Skip this entity as it's likely a vendor name
        }
      }

      // Try to extract currency from the text with context
      currency = _extractCurrencyFromText(entityText);

      if (amount != null) {
        return {
          'amount': amount,
          'currency': currency,
        };
      }
    } catch (e) {
      // Fallback to text parsing - handle thousands separators properly
      final cleanText = entity.text.replaceAll(RegExp(r'[^\d.,]'), '');
      // Remove thousands separators (commas) but preserve decimal point
      final normalizedText = cleanText.replaceAll(',', '');
      final amount = double.tryParse(normalizedText);
      if (amount != null) {
        return {
          'amount': amount,
          'currency': _extractCurrencyFromText(entity.text),
        };
      }
    }
    return null;
  }

  /// Smart amount selection logic to choose between ML Kit and AmountUtils candidates
  Map<String, dynamic>? _selectBestAmount(
    List<Map<String, dynamic>> mlKitCandidates,
    double amountUtilsAmount,
    String fullText,
  ) {
    if (mlKitCandidates.isEmpty) {
      return {
        'amount': amountUtilsAmount,
        'currency': null,
        'source': 'amountUtils',
      };
    }

    // Check if AmountUtils found an amount with abbreviations (k/m/b)
    final hasAbbreviation = _hasAbbreviation(fullText);

    for (final candidate in mlKitCandidates) {
      final mlKitAmount = candidate['amount'] as double;

      // Prefer amounts with abbreviations over plain numbers
      if (hasAbbreviation && amountUtilsAmount > mlKitAmount) {
        return {
          'amount': amountUtilsAmount,
          'currency': candidate['currency'],
          'source': 'amountUtils',
        };
      }

      // Prefer larger amounts when one is >20x the other (likely vendor name vs real amount)
      if (amountUtilsAmount > mlKitAmount * 20) {
        return {
          'amount': amountUtilsAmount,
          'currency': candidate['currency'],
          'source': 'amountUtils',
        };
      }
    }

    // Fall back to first ML Kit result if no clear winner
    final firstCandidate = mlKitCandidates.first;
    return {
      'amount': firstCandidate['amount'],
      'currency': firstCandidate['currency'],
      'source': 'mlKit',
    };
  }

  /// Extract amounts that are not embedded in vendor names
  /// This method finds all potential amounts and filters out vendor-embedded ones
  /// Prefers amounts with abbreviations (k/m/b) over plain numbers
  Map<String, dynamic>? _extractNonVendorAmount(String text) {
    // Find all potential number patterns in the text
    final numberPattern = RegExp(r'\b\d+(?:\.\d+)?(?:[kmb]|k|m|b|million|thousand|billion)?\b', caseSensitive: false);
    final matches = numberPattern.allMatches(text);

    List<Map<String, dynamic>> candidates = [];

    for (final match in matches) {
      final numberText = match.group(0)!;

      // Skip if this number is embedded in a vendor name
      if (_isEmbeddedInVendorName(numberText, text)) {
        continue;
      }

      // Try to parse this as an amount using AmountUtils
      final result = AmountUtils.extractAmountFromText(numberText);
      if (result != null && result['amount'] != null) {
        candidates.add({
          'result': result,
          'text': numberText,
          'hasAbbreviation': _hasAbbreviation(numberText),
        });
      }
    }

    if (candidates.isEmpty) {
      return null;
    }

    // Prefer amounts with abbreviations over plain numbers
    final abbreviatedCandidates = candidates.where((c) => c['hasAbbreviation'] as bool).toList();
    if (abbreviatedCandidates.isNotEmpty) {
      return abbreviatedCandidates.first['result'] as Map<String, dynamic>;
    }

    // Fall back to first plain number
    return candidates.first['result'] as Map<String, dynamic>;
  }

  /// Check if the detected amount appears within an alphabetic sequence like "Lux68" or "Restaurant123"
  bool _isEmbeddedInVendorName(String entityText, String fullText) {
    // Find the position of the entity text in the full text
    final entityIndex = fullText.indexOf(entityText);
    if (entityIndex == -1) return false;

    // Check if the number has letters immediately before it (vendor name pattern)
    final beforeIndex = entityIndex - 1;
    final afterIndex = entityIndex + entityText.length;

    final hasLetterBefore = beforeIndex >= 0 &&
        RegExp(r'[A-Za-z]').hasMatch(fullText[beforeIndex]);
    final hasLetterAfter = afterIndex < fullText.length &&
        RegExp(r'[A-Za-z]').hasMatch(fullText[afterIndex]);

    // Consider it embedded if:
    // 1. Letters both before and after (like "Lux68City")
    // 2. Letters before and followed by space/end (like "Restaurant123 ")
    // 3. Multiple consecutive letters before (like "Restaurant123")
    if (hasLetterBefore && hasLetterAfter) {
      return true; // Classic embedded case like "Lux68City"
    }

    if (hasLetterBefore) {
      // Check if there are multiple letters before (indicating a vendor name)
      int letterCount = 0;
      for (int i = beforeIndex; i >= 0 && RegExp(r'[A-Za-z]').hasMatch(fullText[i]); i--) {
        letterCount++;
      }
      // If 3+ letters before the number, likely a vendor name
      return letterCount >= 3;
    }

    return false;
  }

  /// Check if the entity text has clear currency context (symbols before/after)
  bool _hasCurrencyContext(String entityText) {
    // Check for currency symbols or clear separators
    return RegExp(r'[\$€£¥₹₽¢₩₪₫₱₦₡₨₴₸₼₾₿]|USD|EUR|GBP|JPY|CNY|VND').hasMatch(entityText);
  }

  /// Check if the full text contains abbreviation patterns
  bool _hasAbbreviation(String text) {
    return RegExp(r'\d+[kKmMbB]\b').hasMatch(text);
  }

  /// Parse datetime entity from ML Kit
  DateTime? _parseDateTimeEntity(EntityAnnotationBase entity) {
    try {
      // For now, we'll use the current date as ML Kit entity extraction
      // doesn't directly provide parsed DateTime objects in this version
      return DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Remove entity text from the remaining text
  String _removeEntityFromText(String text, EntityAnnotationBase entity) {
    final start = entity.start;
    final end = entity.end;
    
    if (start >= 0 && end <= text.length && end > start) {
      return (text.substring(0, start) + text.substring(end)).trim();
    }
    return text;
  }

  /// Fallback regex amount extraction (from original parser)
  Map<String, dynamic>? _extractAmountWithRegex(String text) {
    final normalizedText = text.toLowerCase();

    bool isNegative = false;
    String processedText = normalizedText;

    if (normalizedText.startsWith('-')) {
      isNegative = true;
      processedText = normalizedText.substring(1).trim();
    }

    final result = _extractPositiveAmount(processedText);
    if (result != null) {
      result['isNegative'] = isNegative;
    }

    return result;
  }

  /// Helper to extract positive amount values from text with abbreviation support
  Map<String, dynamic>? _extractPositiveAmount(String text) {
    // Use AmountUtils for enhanced amount extraction with abbreviation support
    final result = AmountUtils.extractAmountFromText(text);

    if (result != null) {
      // AmountUtils already handles currency detection, but we maintain the existing
      // currency detection logic as fallback for backward compatibility
      String? currency = result['currency'] as String?;
      if (currency == null) {
        currency = _extractCurrencyFromText(text);
      }

      return {
        'amount': result['amount'] as double,
        'currency': currency,
      };
    }

    return null;
  }

  /// Extract currency information from text
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Remove amount from text for better category detection
  String _removeAmountFromText(String text, double amount) {
    final amountStr = amount.toString();
    return text
        .replaceAll(RegExp(r'\$?\s?' + RegExp.escape(amountStr) + r'\s?(?:dollars|USD|\$|€|EUR|£|GBP)?'), '')
        .trim();
  }

  /// Detect transaction type (from original parser)
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final normalizedText = text.toLowerCase().trim();

    if (RegExp(r'^\s*-').hasMatch(normalizedText) || isNegativeAmount) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
        .hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(normalizedText)) {
      return TransactionType.income;
    }
    
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(normalizedText)) {
      return TransactionType.loan;
    }
    
    if (RegExp(r'\bfor\b').hasMatch(normalizedText) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'[$€£¥]').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }

  /// Build transaction from learned association
  Future<Transaction> _buildTransactionFromAssociation(String text, LearnedAssociation association) async {
    // Try to extract amount from text, fallback to 0.0 if not found
    double amount = 0.0;

    // Get default currency from storage instead of hardcoding USD
    final defaultCurrency = await _storageService.getDefaultCurrency();

    // Enhanced amount extraction for learned associations with abbreviation support
    final amountResult = AmountUtils.extractAmountFromText(text);
    if (amountResult != null) {
      amount = amountResult['amount'] as double;
    }

    // Extract currency from text, fallback to default currency
    String currencyCode = _extractCurrencyFromText(text) ?? defaultCurrency;

    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: association.type ?? TransactionType.expense,
      categoryId: association.categoryId ?? 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: currencyCode,
    );
  }

  /// Dispose resources
  void dispose() {
    _entityExtractor?.close();
  }
}
